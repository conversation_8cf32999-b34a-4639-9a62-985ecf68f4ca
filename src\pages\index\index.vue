<template>
	<view>
		<!-- 头部搜索栏 -->
		<view class="ggbg">
			<view class="d-bf p-20">
				<u-avatar :src="logo" size="64rpx" shape="circle"></u-avatar>
				<view style="width: 540rpx;" @click="goSearch()">
					<u-search height="60" :showAction="false" searchIconSize="50" :animation="true" :value="searchValue"
						color="#a8a8a8"></u-search>
				</view>
				<view class="iconfont icon-fontclass-wenzhangshu" style="color: #F5F5F5;font-size:35rpx"
					@click="navShow = !navShow"></view>
			</view>
		</view>
		<!-- 轮播图 -->
		<view>
			<view class="m-20" style="margin-top: -195rpx;">
				<u-swiper :list="list1" imgMode="scaleToFill" height="440" radius="10" keyName="src"
					@click="homepagejump($event, 'bigSlideshow')">
				</u-swiper>
			</view>
		</view>
		<!-- 公告 -->
		<view class="m-20 bg-white radius15 pl_15 pr_15 f fac fjsb" style="height:72rpx;" v-if="noticeTheHidden"
			@click="goNoticeList">
			<view class="iconfont icon-gonggao" style="color: #00001C;font-size:30rpx">
			</view>
			<u-notice-bar :text="list2" icon="" direction="column" color="#6E6E79" bgColor="#fff"
				fontSize="26"></u-notice-bar>
			<view class="fs-3 ml-5 iconfont icon-member_right" style="color: #7a7a7a;"></view>
		</view>
		<!-- 金刚区 -->
		<view class="m-20 bg-white radius15 bsbb pt-25" style="height: 202rpx;" v-if="list3.length > 0">
			<u-grid :border="false" :col="list3.length">
				<template v-for="(item, index) in list3">
					<u-grid-item :key="item.id" @click="homepagejump(index, 'kingkim')">
						<u--image :showLoading="true" width="96rpx" height="96rpx" :src="item.logo">
						</u--image>
						<text class="font_size12 mt-20">{{ item.title }}</text>
					</u-grid-item>
				</template>
			</u-grid>
		</view>
		<!-- 商品专区 -->
		<view class="m-20 f fac fjsb" v-if="introductionNum !== 0">
			<!-- 直播热卖 -->
			<template v-if="wap_product_introduction.live_type === 1">
				<view class="radius15 f1 f fac fjsb live-box p-25" style="position: relative;" @click="hintSid"
					v-if="introductionNum !== 3">
					<view class="f1">
						<view class="title fs-2-5">
							{{ wap_product_introduction.live_title ? wap_product_introduction.live_title : '直播热卖' }}
						</view>
						<view class="introduction-content mt-15">
							{{ wap_product_introduction.live_introduction ? wap_product_introduction.live_introduction :
								'实时互动热销精选' }}
						</view>
					</view>
					<u--image :showLoading="true" width="98rpx" height="94rpx"
						:src="wap_product_introduction.live_logo ? wap_product_introduction.live_logo : 'https://yunzhong.gz.cn/uploads/file/1deb4d213cd136fa12e59414e9d356e2_20250212150622.png'">
					</u--image>
					<view style="width: 100%;height: 100%; position: absolute; top: 0; left: 0" v-if="sid && checkWenxinType">
						<wx-open-launch-weapp
							style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: inline-block; border: none;"
							:appid="appid" :path="'packageC/live/listsid=' + sid">
							<script type="text/wxtag-template">
                                <style>
                                    .btn {
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
										opacity: 0;
                                    }
                                </style>
                                <button class="btn"></button>
                            </script>
						</wx-open-launch-weapp>
					</view>
				</view>
				<view class="radius15 f1 live-box p-15" style="position: relative;" @click="hintSid" v-else>
					<view class="title fs-2">
						{{ wap_product_introduction.live_title ? wap_product_introduction.live_title : '直播热卖' }}
					</view>
					<view class="f fac fjsb" style="height: 94rpx;">
						<view class="introduction-content">{{ wap_product_introduction.live_introduction ?
							wap_product_introduction.live_introduction : '实时互动热销精选' }}</view>
						<u--image :showLoading="true" width="98rpx" height="94rpx"
							:src="wap_product_introduction.live_logo ? wap_product_introduction.live_logo : 'https://yunzhong.gz.cn/uploads/file/1deb4d213cd136fa12e59414e9d356e2_20250212150622.png'">
						</u--image>
					</view>

					<view style="width: 100%;height: 100%; position: absolute; top: 0; left: 0" v-if="sid && checkWenxinType">
						<wx-open-launch-weapp
							style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: inline-block; border: none;"
							:appid="appid" :path="'packageC/live/list?sid=' + sid">
							<script type="text/wxtag-template">
                                <style>
                                    .btn {
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        width: 100%;
                                        height: 100%;
                                        opacity: 0;
                                    }
                                </style>
                                <button class="btn"></button>
                            </script>
						</wx-open-launch-weapp>
					</view>
				</view>
			</template>
			<!-- 商品素材 -->
			<template v-if="wap_product_introduction.material_type === 1">
				<view class="radius15 f1 f fac fjsb material_box p-25" @click="goMaterial"
					:style="{ marginLeft: wap_product_introduction.live_type === 1 ? '20rpx' : '0rpx' }"
					v-if="introductionNum !== 3">
					<template>
						<view class="f1">
							<view class="title fs-2-5">{{ wap_product_introduction.material_title ?
								wap_product_introduction.material_title : '商品素材' }}</view>
							<view class="introduction-content  mt-15">{{ wap_product_introduction.material_introduction
								?
								wap_product_introduction.material_introduction : '精选素材创意无限' }}</view>
						</view>
						<u--image :showLoading="true" width="98rpx" height="94rpx"
							:src="wap_product_introduction.material_logo ? wap_product_introduction.material_logo : 'https://yunzhong.gz.cn/uploads/file/ddc6479d63366a8436874a7096353584_20250212151229.png'">
						</u--image>
					</template>
				</view>
				<view class="radius15 f1 material_box p-15" @click="goMaterial"
					:style="{ marginLeft: wap_product_introduction.live_type === 1 ? '20rpx' : '0rpx' }"
					v-else>
					<view class="title fs-2">{{ wap_product_introduction.material_title ?
						wap_product_introduction.material_title : '商品素材' }}</view>
					<view class="f fac fjsb" style="height: 94rpx;">
						<view class="introduction-content">{{ wap_product_introduction.material_introduction ?
							wap_product_introduction.material_introduction : '精选素材创意无限' }}</view>
						<u--image :showLoading="true" width="98rpx" height="94rpx"
							:src="wap_product_introduction.material_logo ? wap_product_introduction.material_logo : 'https://yunzhong.gz.cn/uploads/file/ddc6479d63366a8436874a7096353584_20250212151229.png'">
						</u--image>
					</view>
				</view>
			</template>
			<!-- 商品视频 -->
			<template v-if="wap_product_introduction.video_type === 1">
				<view class="radius15 f1 f fac fjsb video_box p-25" @click="goVideo"
					:style="{ marginLeft: wap_product_introduction.live_type === 1 || wap_product_introduction.material_type === 1 ? '20rpx' : '0rpx' }"
					v-if="introductionNum !== 3">
					<view class="f1">
						<view class="title fs-2-5">{{ wap_product_introduction.video_title ?
							wap_product_introduction.video_title : '商品视频' }}</view>
						<view class="introduction-content mt-15">{{ wap_product_introduction.video_introduction ?
							wap_product_introduction.video_introduction : '生动展示直观体验' }}</view>
					</view>
					<u--image :showLoading="true" width="98rpx" height="94rpx"
						:src="wap_product_introduction.video_logo ? wap_product_introduction.video_logo : 'https://yunzhong.gz.cn/uploads/file/1a20ec5e29f6d1a7dc53d4f5766fbb5d_20250212151422.png'">
					</u--image>
				</view>
				<view class="radius15 f1 ml-20 video_box p-15" @click="goVideo" v-else>
					<view class="title fs-2">{{ wap_product_introduction.video_title ?
						wap_product_introduction.video_title : '商品视频' }}</view>
					<view class="f fac fjsb" style="height: 94rpx;">
						<view class="introduction-content">{{ wap_product_introduction.video_introduction ?
							wap_product_introduction.video_introduction : '生动展示直观体验' }}</view>
						<u--image :showLoading="true" width="98rpx" height="94rpx"
							:src="wap_product_introduction.video_logo ? wap_product_introduction.video_logo : 'https://yunzhong.gz.cn/uploads/file/1a20ec5e29f6d1a7dc53d4f5766fbb5d_20250212151422.png'">
						</u--image>
					</view>
				</view>
			</template>
		</view>
		<!-- 共享专区 -->
		<view class="m-20 radius15 bg-white" style="height: 279rpx;" v-if="list4.length !== 0">
			<view class="f fac fjsb" style="height: 90rpx;">
				<view class="f fac ml-18">
					<view class="title fs-2-5">{{ shared.shared_title ? shared.shared_title : '共享专场' }}</view>
					<view class="session-intro">{{ shared.shared_introduction ? shared.shared_introduction : '共享精选便捷购物'
					}}
					</view>
				</view>
				<view class="d-f more-box mr-18" @click="navTo('/packageC/album/albumList')">
					<view class="fs-1-5 d-cc p-10 pl_20 " style="color: #FFFFFF;">
						更多<view class="fs-3 ml-5 iconfont icon-member_right"></view>
					</view>
				</view>
			</view>
			<u-grid :border="false" :col="3">
				<template v-for="item in list4">
					<u-grid-item :key="item.id"
						@click="navTo('/packageC/album/albumDetail?goods_id=' + item.product_album.id)">
						<view class="radius15" style="overflow: hidden;height: 170rpx;background-color: #F5F5F5;">
							<u--image :showLoading="true" width="210rpx" height="120rpx"
								:src="item.product_album.covers && item.product_album.covers.length > 0 ? item.product_album.covers[0].src : ''">
							</u--image>
							<view class="font_size12 f fac fjc" style="height: 48rpx;">{{ item.product_album.name }}
							</view>
						</view>
					</u-grid-item>
				</template>
			</u-grid>
		</view>
		<!-- 主频道 -->
		<view class="m-20" style="height: 200rpx;" v-if="channel_boxes.channel_images.length !== 0">
			<!-- 主频道超过2个时采用轮播图 -->
			<view class="bg-white" v-if="channel_boxes.channel_images.length > 2">
				<u-swiper :list="channel_boxes.channel_images" height="200" radius="16" keyName="h5_src"
					@click="homepagejump($event, 'leftBigSlideshow')"></u-swiper>
			</view>
			<view class="f fac fjsb" v-else>
				<template v-for="(item, index) in channel_boxes.channel_images">
					<u--image @click="homepagejump(index, 'leftBigSlideshow')" :key="index" :showLoading="true"
						height="200rpx" :width="channel_boxes.channel_images.length === 1 ? '710' : '347rpx'"
						radius="16" :src="item.h5_src">
					</u--image>
				</template>
			</view>
		</view>
		<!-- 副频道 -->
		<view class="m-20 f fac fjsb">
			<template v-for="(item, index) in channel_boxes.channel_cards">
				<view class="bg-white radius15 f1 pt-20 pl-10 pr-10 pb-15 bsbb" :key="item.id"
					:style="{ marginLeft: index === 0 ? 0 : '20rpx' }"
					@click="homepagejump(item.banner[0], 'lowRight')">
					<view class="title fs-1-5">{{ item.title }}</view>
					<view class="subchannel mt-10">{{ item.desc }}</view>
					<u--image class="mt-15" radius="12" :showLoading="true" width="100%" :height="dataHeight"
						:src="item.banner[0].h5_src">
					</u--image>
				</view>
			</template>
		</view>
		<!-- 发现好店 -->
		<view class="m-20 discover" style="height: 456rpx;" v-if="list5.length > 0">
			<view class="f fac fjsb" style="height: 83rpx;">
				<view class="ml-20 fs-3 fw-b c-white">发现好店</view>
				<view class="d-f mr-18" @click="goStoreList">
					<view class="fs-1-5 d-cc p-10 pl_20 c-white">
						更多<view class="fs-3 ml-5 iconfont icon-member_right"></view>
					</view>
				</view>
			</view>

			<scroll-view scroll-x="true" class="scroll-x">
				<view class="f" style="width: 710rpx;">
					<template v-for="(item, index) in list5">
						<view class="store-card ml-20" :key="item.id" v-if="item.product.length !== 0">
							<view class="f fac fjsb" style="height: 88rpx;">
								<view class="f fac ml-20 fs-2-5 title" style="width: 370rpx;"
									@click="goStore(item.supplier.id, 0)">
									<u--image :showLoading="true" height="26rpx" width="30rpx"
										src="https://yunzhong.gz.cn/uploads/file/03a0d83ab98816f64b3dc533e3596928_20250213155852.png">
									</u--image>
									<view class="title fs-2-5 ml-10 limit-text-1">{{ item.supplier.shop_name }}</view>
									<view class="fs-3 ml-5 iconfont icon-member_right"></view>
								</view>
								<view class="d-f mr-18 c-gray4 fs-2">
									热销 {{ item.supplier.goods_count }}
								</view>
							</view>
							<u-grid :border="false" :col="3" class="mb-20">
								<template v-for="itemList in item.product">
									<u-grid-item :key="itemList.id" @click="goCommodity(itemList.id)">
										<u--image radius="16" :showLoading="true" width="160rpx" height="160rpx"
											:src="itemList.thumb">
										</u--image>
										<text class="limit-text-1 font_size12 mt-15" style="width:160rpx">{{
											itemList.title
										}}</text>
										<text class="fs-1-5 mt-15 c-theme" style="width:160rpx"
											v-if="checkNull(user)">{{ '¥' +
												toYuan(itemList.normal_price) }}</text>
										<text class="fs-1-5 mt-15 c-theme" style="width:160rpx" v-else>价格登录可见</text>
									</u-grid-item>
								</template>
							</u-grid>
						</view>
					</template>
				</view>
			</scroll-view>
		</view>
		<!-- 移动端列表 -->
		<template v-for="(item, index) in list6">
			<view :key="item.wapHomeSetting.id">
				<view class="f fac fjsb m-20" style="height: 48rpx;">
					<view class="f fac ml-20 title fs-2-5" style="width: 370rpx;">
						<u--image :showLoading="true" height="32rpx" width="30rpx" :src="item.wapHomeSetting.logo">
						</u--image>
						<view class="title fs-2-5 ml-10 limit-text-1">{{ item.wapHomeSetting.title }}</view>
					</view>
					<view class="d-f mr-18" @click="goClassify(item.wapHomeSetting.id)">
						<view class="fs-1-5 d-cc p-10 pl_20 c-gray4">
							查看更多<view class="fs-3 ml-5 iconfont icon-member_right"></view>
						</view>
					</view>
				</view>
				<!-- 商品 -->
				<template v-if="item.wapHomeSetting.data_type === 1">
					<moveGoods ref="moveGoods" :list="item.list"></moveGoods>
				</template>
				<!-- 共享专辑 -->
				<template v-else-if="item.wapHomeSetting.data_type === 2">
					<moveAlbum ref="moveAlbum" :list="item.list"></moveAlbum>
				</template>
				<!-- 供应商 -->
				<template v-else-if="item.wapHomeSetting.data_type === 3">
					<moveSuppliers ref="moveSuppliers" :list="item.list"></moveSuppliers>
				</template>
			</view>
		</template>
		<!-- 侧边栏 -->
		<u-popup class="popup-box" :overlay="true" :show="navShow" @close="closeShade" mode="top" customStyle="min-height: 70rpx">
			<scroll-view scroll-x="true" class="scroll-x">
				<view class="f" style="width: 710rpx">
					<view v-for="(items,index) in list" :class="navIndex === index ? 'nav-color' : ''" :key="index" class="nav-card" @click="chooseNav(items, index)">
						<view class="f">
							<text>{{ items.title }}</text>
							<u-icon v-if="items.show" class="ml_5" name="arrow-down" :color="navIndex === index ? 'nav-color' : ''"></u-icon>
						</view>
					</view>
				</view>
			</scroll-view>
			<u-divider v-if="navChildList.length !== 0"></u-divider>
			<view>
				<view class="d-f pl_25 pr_20 flow">
					<view v-show="item.app_show == 1 && H5orWX === 'H5' ? item.h5_url: item.mini_url" style="background-color: #e8e8e8;color: #4f4f4f; height: 72rpx;"
						class="f fac fjc pr-35 pl-35 bsbb radius8 ml-20 mb-20" v-for="(item, index) in navChildList"
						:key="index" @click="homepagejump(index, 'childNotice')">
							{{ item.title }}
					</view>
				</view>
			</view>
			<view v-if="list.length == 0" style="margin: auto" >暂无数据~</view>
		</u-popup>
		<!-- 返回顶部 -->
		<toTop :flage="flage"></toTop>
		<!-- 提示信息 -->
		<u-toast ref="uToast"></u-toast>
		<!-- tabbar -->
		<myTabBar ref="myTabBar"></myTabBar>
	</view>
</template>

<script>
import myTabBar from "../../components/tabbar.vue";
import toTop from "../../common/toTop/toTop.vue";
import moveGoods from "../../common/moveGoods/moveGoods.vue";
import moveAlbum from "../../common/moveAlbum/moveAlbum.vue";
import moveSuppliers from "../../common/moveSuppliers/moveSuppliers.vue";

export default {
	components: {
		toTop,
		myTabBar,
		moveGoods,
		moveAlbum,
		moveSuppliers
	},
	data() {
		return {
			user: {},
			flage: '', //返回顶部按钮是否显示

			logo: '', // 商城logo
			searchValue: '', // 默认搜索词

			navShow: false, // 侧边栏开关
			navIndex: null,  // 侧边栏下标
			list: [], // 侧边栏数组
			navChildList: [], // 侧边栏子数组
			list1: [], // 轮播图列表
			list2: [], // 公告
			list3: [], // 金刚区
			list4: [], // 共享专区
			list5: [], // 店铺
			list6: [], // 移动端首页列表 //商品 供应商 专辑

			shared: {}, // 共享专区名称简介
			channel_boxes: {
				channel_cards: [],
				channel_images: []
			}, // 频道
			wap_product_introduction: {}, // 商品简介区
			introductionNum: 0, // 商品简介区数量

			bigSlideshowMiniType: '', // 判断外部小程序类型
			bigSlideshowMiniId: '',   // 外部小程序ID

			bigSlideshowUrl: '', // 轮播图url地址
			kingKimUrl: '', // 金刚区地址
			leftBigSlideshowUrl: '', // 频道地址
			upperRightUrl: '', // 副频道地址
			noticeId: null, // 文章和商品专辑的ID

			noticeTheHidden: true, // 公告是否显示

			appid: null, // 小商店
			sid: null, //
			checkWenxinType: false, // 是否是微信浏览器 / 公众号
			H5orWX: 'WX'
		}
	},
	computed: {
		dataHeight() {
			let height = ''
			switch (this.channel_boxes.channel_cards.length) {
				case 4:
					height = '96rpx'
					break;
				case 3:
					height = '130rpx'
					break;
				case 2, 1:
					height = '160rpx'
					break;
			}
			return height
		},
	},
	onShow() {
		this.$nextTick(() => {
			this.$refs.myTabBar.tabBarActive = 0
		})
		this.user = uni.getStorageSync('user')
		if (this.checkNull(this.user) && uni.getStorageSync('type') == 'refresh') { //如果登录成功跳转到该页面,就重新获取商品列表
			uni.setStorageSync('type', "")
			this.list6 = [];
			this.gettingData() // 获取轮播图 金刚区 商品简介区 共享专区 频道
			this.getStore() // 获取店铺
			this.getProduct() // 获取移动端列表
			this.getStore() // 获取店铺

		}
	},
	onLoad(e) {
		// #ifdef H5
		this.H5orWX = 'H5'
		// #endif
		// #ifdef H5
		if (e.invite_code) {
			// pc端携带邀请码，转换成h5地址，保存激情码
			uni.setStorageSync('invite_code', window.atob(e.invite_code))
		}
		// #endif
		this.user = uni.getStorageSync('user')
		// #ifdef H5
		this.setWeixinShare()
		this.getPid() // 查看pid
		// #endif
		// #ifdef MP-WEIXIN
		this.getMiniPid(e)
		// #endif
		this.getTheTitle() // 获取页面标题和默认搜索词
		this.announcement() // 获取公告
		this.gettingData() // 获取轮播图 金刚区 商品简介区 共享专区 频道
		this.getProduct() // 获取移动端列表
		this.getStore() // 获取店铺
    if(this.isLogin()){
      this.onLoadSetting() // 获取店铺设置sid
    }
		// #ifdef MP-WEIXIN
		this.isMPWEIXIN = true
		// #endif

		// 获取appId
		this.checkWenxinType = this.checkWenxin()
		// if (this.checkWenxin() || this.isMPWEIXIN && this.isLogin()) {
		if (this.isMPWEIXIN && this.isLogin()) {
			this.get('/api/smallShop/setting/getMiniAppID',{},true,false,false).then(res => {
				this.appid = res.data.appid
			})
		}
		// 微信浏览器
		if (this.checkWenxin()) {
			const url = window.location.href.split('#')[0]
			this.get('/api/wechatofficial/getJsConfig', {
				url,
			}).then(res => {
				if (res.code === 0) {
					const data = res.data
					jweixin.config({
						debug: false,
						appId: data.app_id,
						timestamp: data.timestamp,
						nonceStr: data.nonce_str,
						signature: data.signature,
						jsApiList: ['chooseImage'],
						openTagList: ['wx-open-launch-weapp'],
					})
				}
			})
		}
	},

	methods: {
		// 分享触发的事件
		onShareAppMessage() {
			let pid = ''
			if (uni.getStorageSync('user')) {
				pid = parseInt(uni.getStorageSync('user').id)
			}
			return {
				title: this.shareTitle,
				path: `pages/index/index?invite_code=${pid}`
			}
		},
		jumpLogin() {
			if (this.checkWenxin() && !this.checkNull(this.user)) {
				// 公众号登录
				this.isWeiXinBrowser()
				return;
			}
			if (!this.user) {
				this.navTo("/pages/login/login")
			}
		},
		// 获取H5端的pid
		async getPid() {
			// 没有pid不做任何处理
			if (this.$route.query.pid) {
				// 有pid判断是否登录
				if (this.user) {
					// 已登录清除本地
					localStorage.removeItem('pid')
					await this.post("/api/user/bindParent", { pid: parseInt(this.$route.query.pid) }, false, false, false)
				} else {
					// 未登录将pid存储到本地
					localStorage.setItem('pid', this.$route.query.pid)
				}
			}
		},
		// 获取微信小程序端的pid
		async getMiniPid(e) {
			// 没有pid不做任何处理
			if (e.pid) {
				// 有pid判断是否登录
				if (this.user) {
					// 已登录清除本地
					uni.removeStorageSync('pid')
					await this.post("/api/user/bindParent", { pid: parseInt(e.pid) }, false, false, false)
				} else {
					// 未登录将pid存储到本地
					uni.setStorageSync('pid', e.pid)
				}
			}
		},
		getTheTitle() { //获取页面标题和默认搜索词
			this.get('/api/home/<USER>', {}, true).then(data => {
				uni.setNavigationBarTitle({
					title: data.data.tab.title
				})
				uni.setStorageSync('tabTitle', data.data.tab.title)
				uni.setStorageSync('h5_logo', data.data.header.logo_square_src)
				this.tabTitle = data.data.tab.title
				this.shareTitle = data.data.header.welcome
				this.searchValue = data.data.header.search.place_holder;
				this.logo = data.data.header.mobile_logo
			})
		},
		gettingData() { //轮播图 金刚区
			this.get('/api/home/<USER>', {}, true).then(data => {
				let quick_navigation = []
				// #ifdef H5
				data.data.quick_navigation.forEach(element => {
					if (element.child) {
						element.title =  element.title
					}
					if (element.h5_url && element.app_show == 1) {
						quick_navigation.push(element)
					}
				});
				// #endif
				// #ifdef MP-WEIXIN
				data.data.quick_navigation.forEach(element => {
					if (element.child) {
						element.title =  element.title
					}
					if (element.app_show == 1) {
						if (element.mini_url_type === 0 || element.mini_url_type) {
							quick_navigation.push(element)
						}
					}
				});
				// #endif

				// 弹窗
				quick_navigation.forEach(element => {
					if (element.child) {
						element.show = !element.child.every(childs => childs.app_show == 0)
					} else {
						element.show = false
					}
				})
				this.list = quick_navigation
				// 轮播图
				this.list1 = data.data.ads
				// 金刚区
				this.list3 = data.data.wap_diamond_area;
				let introductionNum = 0
				// 商品简介区
				this.wap_product_introduction = data.data.wap_product_introduction;
				if (data.data.wap_product_introduction.live_type === 1) {
					introductionNum++
				}
				if (data.data.wap_product_introduction.material_type === 1) {
					introductionNum++
				}
				if (data.data.wap_product_introduction.video_type === 1) {
					introductionNum++
				}
				this.introductionNum = introductionNum;
				// 共享专区
				this.list4 = data.data.wap_shared_special_session.WapSharedSpecialSession ? data.data.wap_shared_special_session.WapSharedSpecialSession : [];
				this.shared = {
					shared_introduction: data.data.wap_shared_special_session.shared_introduction,
					shared_title: data.data.wap_shared_special_session.shared_title
				}
				// 频道
				data.data.channel_boxes.channel_images.forEach(item => {
					item.h5_src = item.h5_src ? item.h5_src : item.src
				})
				data.data.channel_boxes.channel_cards = data.data.channel_boxes.channel_cards.length > 3 ? data.data.channel_boxes.channel_cards.slice(0, 4) : data.data.channel_boxes.channel_cards;
				data.data.channel_boxes.channel_cards.forEach(item => {
					// 副频道只展示 频道组下标0的频道
					item.banner[0].h5_src = item.banner[0].h5_src ? item.banner[0].h5_src : item.banner[0].src;
				})
				this.channel_boxes = data.data.channel_boxes;
			})
		},
		goSearch() { //跳转到搜索详情页面
			this.navTo('/packageA/search/search')
		},
		announcement() { //获取公告
			this.get('/api/home/<USER>', {}, true).then(data => {
				// 判断公告是否显示
				if (data.data.length == 0 || data.data[0].child == undefined && data.data[1].child == undefined) {
					this.noticeTheHidden = false
				}
				let list = []
				data.data.forEach(item => {
					list.push(item.title)
				})
				this.list2 = list
			})
		},
		// 进入素材中心
		goMaterial() {
			this.navTo('/packageE/marketingTool/materialCenter/materialCenter')
		},
		// 进入视频中心
		goVideo() {
			this.navTo('/packageE/marketingTool/miniVideo/miniVideo?pause=true')
		},
		// 进入直播
		goLive() {
			// #ifdef MP-WEIXIN
			uni.navigateToMiniProgram({
				appId: this.appid, // 要打开的小程序 appId
				path: 'pages/index/index?sid=' + this.sid, // 首页
				success(res) {},
				fail(res) {},
				complete(res) {},
			})
			// #endif
		},
		// 获取店铺设置
		async onLoadSetting() {
			const res = await this.get(
				'/api/smallShop/setting/getShopSetting',
				{},
				true, false, false
			)
			if (res.code == 0) {
				this.sid = res.data.setting.sid // 小商店店主的id
			}
		},
		// 如果获取null 则无法跳转到小商店
		hintSid() {
			if (this.sid == null) {
				this.$refs.uToast.show({
					type: "error",
					icon: false,
					position: 'top',
					message: "请开通商店"
				})
			} else {
				// #ifdef MP-WEIXIN
				this.goLive()
				// #endif
			}
		},
		// 获取店铺
		getStore() {
			let url = '';
			if (this.user) {
				url = '/api/product/getWapStoreSectionProductsLogin'  // 登入
			} else {
				url = '/api/product/getWapStoreSectionProducts'  // 未登入
			}
			this.post(url, {}, true).then(data => {
				this.list5 = data.data ? data.data : [];
			})
		},
		goNoticeList() { //跳转到文章页面
			this.navTo('/packageB/notice/noticeList')
		},
		// 进入店铺
		goStore(id, type) {
			this.navTo("/packageB/store/storeDetails?id=" + id + "&type=" + type)
		},
		// 进入全部店铺
		goStoreList() {
			this.navTo("/packageD/shopList/shopList")
		},
		// 进入商品详情
		goCommodity(id) {
			this.navTo('/packageA/commodity/commodity_details/commodity_details?id=' + id)
		},
		// 获取移动
		getProduct() {
			let url = '';
			if (this.user) {
				url = '/api/product/getProductByWapHomeSettingsLogin'  // 登入
			} else {
				url = '/api/product/getProductByWapHomeSettings'  // 未登入
			}
			this.post(url, {}, true).then(data => {
				this.list6 = data.data
			})
		},
		closeShade() { //关闭查看更多
			this.navShow = false
		},
		// 点击侧边栏一级导航
		chooseNav(item, index) {
			this.navChildList = []
			this.navIndex = index
			if (!item.child || item.child.every(childs => childs.app_show == 0)) {
				this.homepagejump(index, 'notice')
			} else {
				this.navChildList = item.child
			}
		},
		// 移动端列表
		goClassify(id) {
			this.navTo('/packageC/handpickClassify/handpickClassify?id=' + id)
		},
		homepagejump(index, type) { //首页轮播图商品页面跳转
			let path;
			// #ifdef  APP-PLUS
			this.jumpAssignment(index, type, "app_url")
			// #endif

			// #ifdef  H5
			this.jumpAssignment(index, type, "h5_url",)
			// #endif

			// #ifdef  MP
			this.jumpAssignment(index, type, "mini_url", "mini_type", "mini_id", "mini_url_type")
			// #endif
			/* 点击大轮播图触发 */
			if (type == "bigSlideshow") {
				if (this.list1[index].jump_type == 1) { //1链接2分类3专辑
					path = this.bigSlideshowUrl
					// 小程序中进行跳转
					// #ifdef  MP
					if (this.bigSlideshowMiniType === 0) {
						// 跳转内部链接
						this.externalLinkJudgment(path)
					} else {
						// 跳转外部链接
						uni.navigateToMiniProgram({
							appId: this.bigSlideshowMiniId,
							path: path,
							success(res) {},
							fail(res) {},
							complete(res) {},
						})
					}
					// #endif
					// H5端进行跳转
					// #ifdef  H5
					if (path.includes("http") || path.includes("https")) {
						window.location.href = path
					} else {
						this.externalLinkJudgment(path)
					}
					// #endif
				} else if (this.list1[index].jump_type == 2) {
					this.navTo("/packageA/search/searchResult/searchResult?category1_id=" + this.list1[index]
						.category1_id + '&category2_id=' + this.list1[index].category2_id + '&category3_id=' + this
							.list1[index].category3_id)
				} else if (this.list1[index].jump_type == 3) {
					this.navTo("/packageA/search/searchResult/searchResult?collection_id=" + this.list1[index]
						.collection_id)
				}
			}
			/* 金刚区 */
			else if (type == 'kingkim') {
				path = this.kingKimUrl;
				// H5端进行跳转
				// #ifdef  H5
				if (path.includes("http") || path.includes("https")) {
					window.location.href = path
				} else {
					this.externalLinkJudgment(path)
				}
				// #endif
				// #ifdef  MP
				// 小程序中进行跳转
				if (this.bigSlideshowMiniType === 1) {
					// 商品专辑跳转
					this.navTo("/packageA/search/searchResult/searchResult?collection_id=" + this.noticeId)
				}
				if (this.bigSlideshowMiniType === 2) {
					// 跳转内部链接
					this.externalLinkJudgment(path)
				}
				if (this.bigSlideshowMiniType === 3) {
					// 跳转外部链接
					uni.navigateToMiniProgram({
						appId: this.bigSlideshowMiniId,
						path: path,
						success(res) {},
						fail(res) {},
						complete(res) {},
					})
				}
				// #endif
			}
			/* 主频道 */
			else if (type == "leftBigSlideshow") {
				if (this.channel_boxes.channel_images[index].jump_type == 1) {
					path = this.leftBigSlideshowUrl
					// 小程序中进行跳转
					// #ifdef  MP
					if (this.bigSlideshowMiniType === 0) {
						// 跳转内部链接
						this.externalLinkJudgment(path)
					} else {
						// 跳转外部链接
						uni.navigateToMiniProgram({
							appId: this.bigSlideshowMiniId,
							path: path,
							success(res) {},
							fail(res) {},
							complete(res) {},
						})
					}
					// #endif
					// H5端进行跳转
					// #ifdef  H5
					if (path.includes("http") || path.includes("https")) {
						window.location.href = path
					} else {
						this.externalLinkJudgment(path)
					}
					// #endif
				} else if (this.channel_boxes.channel_images[index].jump_type == 2) {
					this.navTo("/packageA/search/searchResult/searchResult?category1_id=" + this.channel_boxes
						.channel_images[index].category1_id + '&category2_id=' + this.channel_boxes.channel_images[index]
							.category2_id + '&category3_id=' + this.channel_boxes.channel_images[index].category3_id)
				} else if (this.channel_boxes.channel_images[index].jump_type == 3) {
					this.navTo("/packageA/search/searchResult/searchResult?collection_id=" + this.channel_boxes
						.channel_images[index]
						.collection_id)
				}
			}
			/* 副频道 */
			else if (type == "lowRight") {
				if (index.jump_type == 1) {
					path = this.upperRightUrl
					// 小程序中进行跳转
					// #ifdef  MP
					if (this.bigSlideshowMiniType === 0) {
						// 跳转内部链接
						this.externalLinkJudgment(path)
					} else {
						// 跳转外部链接
						uni.navigateToMiniProgram({
							appId: this.bigSlideshowMiniId,
							path: path,
							success(res) {},
							fail(res) {},
							complete(res) {},
						})
					}
					// #endif
					// H5端进行跳转
					// #ifdef  H5
					if (path.includes("http") || path.includes("https")) {
						window.location.href = path
					} else {
						this.externalLinkJudgment(path)
					}
					// #endif
				} else if (index.jump_type == 2) {
					this.navTo("/packageA/search/searchResult/searchResult?category1_id=" + index
						.category1_id + '&category2_id=' + index.category2_id + '&category3_id=' + index
							.category3_id)
				} else if (index.jump_type == 3) {
					this.navTo("/packageA/search/searchResult/searchResult?collection_id=" + index
						.collection_id)
				}
			}
			/* 侧边栏 */
			else if (type == "notice" || type == "childNotice") {
				path = this.noticeUrl
				// 小程序中进行跳转
				// #ifdef  MP
				if (this.bigSlideshowMiniType === 0) {
					// 文章跳转
					this.navTo("/packageB/notice/noticeDetails?id=" + this.noticeId)
				}
				if (this.bigSlideshowMiniType === 1) {
					// 商品专辑跳转
					this.navTo("/packageA/search/searchResult/searchResult?collection_id=" + this.noticeId)
				}
				if (this.bigSlideshowMiniType === 2) {
					// 跳转内部链接
					this.externalLinkJudgment(path)
				}
				if (this.bigSlideshowMiniType === 3) {
					// 跳转外部链接
					uni.navigateToMiniProgram({
						appId: this.bigSlideshowMiniId,
						path: path,
						success(res) {},
						fail(res) {},
						complete(res) {},
					})
				}
				// #endif
				// H5端进行跳转
				// #ifdef  H5
				if (path.includes("http") || path.includes("https")) {
					window.location.href = path
				} else {
					this.externalLinkJudgment(path)
				}
				// #endif
			}
		},
		jumpAssignment(index, type, item, miniItem, miniId, miniType) {
			let listUrl = this.list1[index];
			let kinglist = this.list3[index];
			let leftBigSlideUrl = this.channel_boxes.channel_images[index];
			let upperUrl = index;
			let ntcUrl = this.list[index]
			let ntcChildUrl = this.navChildList[index]
			if (type == "bigSlideshow") {
				this.bigSlideshowUrl = listUrl[item]
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = listUrl[miniItem]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = listUrl[miniId]
			} else if (type == "kingkim") {
				this.kingKimUrl = kinglist[item];
				// 将获的ID进行存储
				this.noticeId = kinglist.mini_collection_id
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = kinglist[miniType]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = kinglist[miniId]
			} else if (type == "leftBigSlideshow") {
				this.leftBigSlideshowUrl = leftBigSlideUrl[item]
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = leftBigSlideUrl[miniItem]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = leftBigSlideUrl[miniId]
			} else if (type == "lowRight") {
				this.upperRightUrl = upperUrl[item]
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = upperUrl[miniItem]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = upperUrl[miniId]
			} else if (type == "notice") {
				this.noticeUrl = ntcUrl[item]
				// 将获的ID进行存储
				this.noticeId = ntcUrl.mini_collection_id
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = ntcUrl[miniType]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = ntcUrl[miniId]
			} else if (type == "childNotice") {
				this.noticeUrl = ntcChildUrl[item]
				// 将获的ID进行存储
				this.noticeId = ntcChildUrl.mini_collection_id
				// 判断外部链接还是内部链接
				this.bigSlideshowMiniType = ntcChildUrl[miniType]
				// 外部链接的小程序ID
				this.bigSlideshowMiniId = ntcChildUrl[miniId]
			}
		},
		onPageScroll(e) { //根据距离顶部距离是否显示回到顶部按钮
			if (e.scrollTop > 10) { //当距离大于10时显示回到顶部按钮
				this.flage = true
			} else {
				this.flage = false
			}
		},
	}
}
</script>

<style scoped>
.color80 {
	color: #808080;
}

.limit-text-1 {
	word-break: break-all;
	text-overflow: ellipsis;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.bsbb {
	box-sizing: border-box;
}

.h23 {
	height: 23rpx;
}

.ml-18 {
	margin-left: 18rpx;
}

.uni-input-input {
	/* 输入框字体大小*/
	font-size: 12rpx;
}

.scroll-x {
	width: 710rpx;
}

.ggbg {
	/* 公告背景*/
	background: linear-gradient(to bottom, #f14e4e, #fd9d9d);
	width: 100%;
	height: 300rpx;
}

::v-deep .u-notice__left-icon {
	display: none;
}

.u-line {
	border-top: none;
}

.u-action-sheet.data-v-6766c527 {
	text-align: left;
}

.live-box {
	background: linear-gradient(180deg, #FFB2BD 0%, #FFFFFF 100%);
	height: 157rpx;
	box-sizing: border-box;
}

.material_box {
	background: linear-gradient(180deg, #DCF3FF 0%, #FFFFFF 100%);
	height: 157rpx;
	box-sizing: border-box;
}

.video_box {
	background: linear-gradient(180deg, #DEDCFF 0%, #FFFFFF 100%);
	height: 157rpx;
	box-sizing: border-box;

}

.title {
	color: #00001C;
	font-weight: bold;
}

.subchannel {
	font-weight: 400;
	font-size: 24rpx;
}

.introduction-content {
	font-weight: 400;
	font-size: 24rpx;
	color: #6E6E79;
}

.session-intro {
	font-weight: 400;
	font-size: 26rpx;
	color: #3B3B4A;
	margin-left: 16rpx;
}

.more-box {
	height: 48rpx;
	background: #F14E4E;
	border-radius: 30rpx;
	color: #FFFFFF;
}

.discover {
	background-image: url('https://yunzhong.gz.cn/uploads/file/67963ff2a1fa31f848a54723d3897dcd_20250213153010.png');
	background-size: cover;
	/* 使图片覆盖整个屏幕 */
	background-position: center;
	/* 将图片居中 */
}

.store-card {
	width: 555rpx;
	height: 353rpx;
	background: linear-gradient(180deg, #FFCCB9 0%, #FFFFFF 100%);
	border-radius: 20rpx;
	flex-shrink: 0;
}

.nav-card {
	flex-shrink: 0;
	padding: 14rpx 20rpx;
}

.nav-color {
	color: #F42121;
}

::v-deep .u-divider {
	margin-top: 0;
}

.popup-box ::v-deep .u-fade-enter-active {
	margin-top: 100rpx;
} 

::v-deep .u-slide-down-enter-active {
	top: 100rpx !important;
}
</style>