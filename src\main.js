import Vue from 'vue'
import App from './App'
// 引入vuex
import store from './store'
// 引入自定义导航栏
// 复制
import VueClipboard from 'vue-clipboard2' // h5复制功能
import { Base64 } from 'pages/promotion/encryptionBase64.js' // base64加密解密
import headerBar from 'common/headerBar/headerBar.vue'
// main.js，注意要在use方法之后执行
import uView from 'uview-ui'
import host from './static/host'
import { ThrottleOrder } from '@/utils/debounce.js'
// 引入全局判断空值的方法
import isEmpty from './utils/isEmpty.js'
import { mixin } from './utils/mixin.js'
import clickoutside from './utils/clickOutside.js'
Vue.component('header-bar', headerBar)
Vue.prototype.code = new Base64()
Vue.use(uView)
Vue.use(VueClipboard)
Vue.use(clickoutside)

// 全局分享
const $x = {}
Vue.prototype.$x = $x
const share = require('utils/share.js')
Vue.mixin(share)
Vue.prototype.$isEmpty = isEmpty
Vue.mixin(mixin)
// 如此配置即可
uni.$u.config.unit = 'rpx'

Vue.config.productionTip = false
Vue.config.ignoredElements = ['wx-open-launch-weapp', /^uni-/]
Vue.prototype.$store = store
App.mpType = 'app'

const app = new Vue({
  ...App,
  store,
})
app.$mount()

const api = {
  // host: 'http://127.0.0.1:8888', // 本地
  // h5请求，无需写死域名，根据当前域名请求：/supplyapi
  // host: 'https://www.zhongkehuayan.cn/supplyapi', //地址
  // host: "https://yunzhong.gz.cn/supplyapi", //地址
  host: '/supplyapi', //地址
  // host: host.httpUrl + '/supplyapi', //地址
}

Vue.prototype.api = api

const that = Vue.prototype

// 提示
Vue.prototype.showSuccess = context => {
  uni.showModal({
    title: '提示',
    content: context,
    showCancel: false,
  })
}

// 加载中
Vue.prototype.loading = msg => {
  uni.showLoading({
    title: msg,
  })
}

// 只需修改xxxxx值，其余无需修改
Vue.prototype.resetSetItem = function (key, newVal) {
  // 实时更新sessionStorage
  if (key === 'xxxxx') {
    // 创建一个StorageEvent事件
    const newStorageEvent = document.createEvent('StorageEvent')
    const storage = {
      setItem: function (k, val) {
        sessionStorage.setItem(k, val)
        // 初始化创建的事件
        newStorageEvent.initStorageEvent(
          'setItem',
          false,
          false,
          k,
          null,
          val,
          null,
          null,
        )
        // 派发对象
        window.dispatchEvent(newStorageEvent)
      },
    }
    return storage.setItem(key, newVal)
  }
}
Vue.prototype.checkWenxin = function () {
  // 判断是否为微信浏览器
  // #ifdef H5
  const ua = window.navigator.userAgent.toLowerCase()
  if (ua.match(/MicroMessenger/i) == 'micromessenger') {
    return true
  } else {
    return false
  }
  // #endif
}
/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
  for (let i = 0; i < this.length; i++) {
    if (this[i][kName] == value) return i
  }
  return -1
}
// 从本地获取token
function token() {
  // console.log(uni.getStorageSync('token'));
  return uni.getStorageSync('token')
}

Vue.prototype.isWeiXinBrowser = function () {
  // 清理URL中的code和state参数，防止再次登录时使用已过期的code
  if (typeof window !== 'undefined' && window.location && window.location.href.includes('code')) {
    let urlParams = new URLSearchParams(window.location.search)
    urlParams.delete('code')
    urlParams.delete('state')
    
    // 构建新的URL
    let newUrl = window.location.protocol + '//'
      + window.location.host + window.location.pathname
      + (urlParams.toString() ? '?' + urlParams.toString() : '')
      + window.location.hash
    
    // 替换当前URL，但不触发页面刷新
    window.history.replaceState({}, document.title, newUrl)
  }

  // 公众号登录
  const windowUrl = window.location.href
  // let pageHref = document.location.protocol + '//' + window.location.host + '/h5/?menu#/pages/membercenter/membercenter'
  let pageHref = ''
  if (windowUrl.includes('code')) {
    pageHref = windowUrl
  } else {
    pageHref =
      document.location.protocol +
      '//' +
      window.location.host +
      '/h5/?menu#/pages/membercenter/membercenter'
  }
  // let url = '/api/wechatofficial/wxLogin?url=' + pageHref.replace('#', '%23')
  let url = ''
  let codeValue = ''
  if (pageHref.includes('code')) {
    const indexHref = pageHref.lastIndexOf('code')
    const extension = pageHref
      .substring(indexHref, pageHref.length)
      .replace('&', ',')
    let arr = []
    arr = extension.split(',')
    codeValue = arr[0].match(/=(\S*)/)[1]
  }
  if (codeValue) {
    url =
      '/api/wechatofficial/wxLogin?url=' +
      pageHref.replace('#', '%23') +
      '&code=' +
      codeValue
  } else {
    url = '/api/wechatofficial/wxLogin?url=' + pageHref.replace('#', '%23')
  }
  that.get(url, {}, true).then(res => {
    const data = res.data
    if (data.code == 202) {
      uni.showToast({
        title: '该账号还未注册,2秒后将跳转到注册页面!',
        icon: 'none',
        duration: 2000,
      })
      setTimeout(
        () =>
          uni.redirectTo({
            url:
              '/pages/register/register?avatar=' +
              data.data?.avatar +
              '&nickname=' +
              data.data?.nickname +
              '&wx_openid=' +
              data.data?.wx_openid +
              '&wx_unionid=' +
              data.data?.wx_unionid +
              '&show=' +
              true +
              '&type=0',
          }),
        2000,
      )
    } else if (data.code == 201) {
      // 还未获取code
      window.location.href = data.redirect_url
    } else {
      let windowUrlNew = window.location.href
      if (windowUrlNew.includes('code')) {
        let urlParams = new URLSearchParams(window.location.search)
        urlParams.delete('code')
        urlParams.delete('state')
        window.location.search = urlParams.toString()
      }
      uni.setStorageSync('token', data.token)
      uni.setStorageSync('user', data.user)
      uni.setStorageSync('type', 'refresh')
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 2000,
      })
      setTimeout(() => {
        // 检查当前页面是否为会员中心页面，如果是则刷新，否则跳转到首页
        const currentPages = getCurrentPages()
        const currentPage = currentPages[currentPages.length - 1]
        const currentRoute = currentPage.route

        if (currentRoute.includes('pages/membercenter/membercenter')) {
          // 如果当前在会员中心页面，则刷新页面
          window.location.reload()
        } else {
          // 否则跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
          })
        }
      }, 1500)
    }
  })
}

// get请求	url		路径
//			params	json 如果不传默认空对象
//			isLoading	如果需要加载中动画就传true，否则默认false
Vue.prototype.get = function (
  url,
  params = {},
  isLoading = false,
  errorJump,
  errorShow = true,
  token = uni.getStorageSync('token'),
) {
  return requestPromise(
    url,
    'GET',
    params,
    isLoading,
    errorJump,
    errorShow,
    token,
  )
}

// post请求	url		路径
Vue.prototype.post = function (
  url,
  params = {},
  isLoading = false,
  errorJump,
  errorShow = true,
  token = uni.getStorageSync('token'),
) {
  return requestPromise(
    url,
    'POST',
    params,
    isLoading,
    errorJump,
    errorShow,
    token,
  )
}
// put更新请求
Vue.prototype.put = function (
  url,
  params = {},
  isLoading = false,
  errorJump,
  errorShow = true,
  token = uni.getStorageSync('token'),
) {
  return requestPromise(
    url,
    'PUT',
    params,
    isLoading,
    errorJump,
    errorShow,
    token,
  )
}
// delect删除请求
Vue.prototype.delete = function (
  url,
  params = {},
  isLoading = false,
  errorJump,
  errorShow = true,
  token = uni.getStorageSync('token'),
) {
  return requestPromise(
    url,
    'DELETE',
    params,
    isLoading,
    errorJump,
    errorShow,
    token,
  )
}

// 创建登录节流
let throttledLogin = ThrottleOrder(
  function () {
    uni.navigateTo({
      url: '/pages/login/login',
    })
  },
  5000,
  false,
)

// method 对应POST,GET,Put,Delete请求
const requestPromise = function (
  url,
  method,
  params,
  isLoading,
  errorJump,
  errorShow,
  token,
) {
  // isLoading	如果需要加载中动画就传true，否则默认false
  if (isLoading) {
    that.loading('加载中')
  }
  // 创建promise函数	并return给requestPromise函数
  // resolve,结果回调函数
  return new Promise(function (resolve, reject) {
    console.log(api.host)
    uni.request({
      url: api.host + url,
      method,
      data: params,
      dataType: 'json',
      header: {
        // 'content-type': 'application/x-www-form-urlencoded',
        'Content-Type': 'application/json',
        'x-token': token,
      },
      success: res => {
        uni.hideLoading()
        const data = res.data.data
        const code = res.data.code
        const msg = res.data.msg
        let permit = true
        if (data.reload) {
          // 如果用户未登录
          const routes = getCurrentPages()
          const pageUrl = routes[routes.length - 1].route

          const arry = [
            'pages/promotion/promotion',
            'pages/shoping_car/shoping_car',
            'pages/membercenter/membercenter',
          ] // 用户未登录放行页面路径
          // if (!that.checkWenxin()) { //不是微信浏览器才放行会员中心页面
          // 	arry.push('pages/membercenter/membercenter')
          // }
          arry.forEach((item, index) => {
            if (pageUrl.indexOf(item) != -1) {
              return (permit = false)
            }
          })
          if (permit) {
            // uni.navigateTo({
            //   url: '/pages/login/login',
            // })
            if (that.checkWenxin()) {
              that
                .get('/api/wechatofficial/isOpenWechat')
                .then(({ code, data }) => {
                  if (code === 0 && data.is_open === 1) {
                    // 公众号登录
                    that.isWeiXinBrowser()
                  } else {
                    throttledLogin()
                  }
                })
            } else {
              throttledLogin()
            }
          }
        }
        if (code === 0) {
          resolve(res.data)
        } else {
          if (errorShow != false) {
            // errorShow传入的值为false,那么就不会弹出报错信息
            if (permit) {
              uni.showToast({
                title: msg,
                icon: 'none',
                duration: 2000, // 持续时间为 2秒
              })
            }
          }
          if (errorJump == true) {
            // 如果接口报错并且errorJump传的值为true,那么就会走then,不传就会提示报错信息并且return
            resolve(res.data)
          } else {
          }
        }
      },
      fail: err => {
        console.log(err)
        uni.hideLoading()
        that.showSuccess('获取失败')
      },
    })
  })
}
